#!/bin/bash

set -e

echo "=== ZeroSSL EAB Secret Encryption Helper ==="
echo

# Check if kubeseal is installed
if ! command -v kubeseal &> /dev/null; then
    echo "❌ kubeseal is not installed. Please install it first:"
    echo "   brew install kubeseal  # macOS"
    echo "   or follow instructions in README.md for Linux"
    exit 1
fi

# Check if kubectl is available and cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

echo "✅ kubeseal and kubectl are available"
echo

# Check if sealed-secrets controller is running
echo "🔍 Checking if sealed-secrets controller is ready..."
if ! kubectl get deployment sealed-secrets-controller -n kube-system &> /dev/null; then
    echo "❌ sealed-secrets controller not found. Please deploy the cert-manager application first."
    exit 1
fi

# Wait for controller to be ready
echo "⏳ Waiting for sealed-secrets controller to be ready..."
kubectl wait --for=condition=Available deployment/sealed-secrets-controller -n kube-system --timeout=300s

echo "✅ sealed-secrets controller is ready"
echo

# Prompt for the secret value
echo "🔐 Please enter your ZeroSSL EAB secret value:"
echo "   (This is the secret value from your ZeroSSL account, not the keyID)"
read -s SECRET_VALUE

if [ -z "$SECRET_VALUE" ]; then
    echo "❌ Secret value cannot be empty"
    exit 1
fi

echo
echo "🔒 Encrypting secret..."

# Encrypt the secret
ENCRYPTED_SECRET=$(echo -n "$SECRET_VALUE" | kubeseal --raw --name zerossl-eab-secret --namespace cert-manager)

if [ $? -ne 0 ]; then
    echo "❌ Failed to encrypt secret"
    exit 1
fi

echo "✅ Secret encrypted successfully!"
echo
echo "📝 Update the following file:"
echo "   base/infrastructure/cert-manager/templates/zerossl-eab-sealedsecret.yaml"
echo
echo "Replace the 'secret:' value with:"
echo "   secret: $ENCRYPTED_SECRET"
echo
echo "🚀 After updating the file, commit and push your changes."
echo "   ArgoCD will automatically sync and create the secret in the cluster."
echo

# Optionally show the full YAML snippet
echo "📋 Complete encryptedData section:"
echo "  encryptedData:"
echo "    secret: $ENCRYPTED_SECRET"
