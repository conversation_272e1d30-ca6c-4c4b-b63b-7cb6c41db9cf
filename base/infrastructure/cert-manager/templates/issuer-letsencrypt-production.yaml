apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: issuer-letsencrypt-production
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: issuer-letsencrypt-production
    solvers:
      - http01:
          gatewayHTTPRoute:
            parentRefs:
            - kind: Gateway
              name: cert-manager-eg
              namespace: default
