apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  name: zerossl-eab-secret
  namespace: cert-manager
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  encryptedData:
    # This is a placeholder - you need to encrypt the actual secret using kubeseal
    # Run: echo -n "your-actual-secret-value" | kubeseal --raw --name zerossl-eab-secret --namespace cert-manager
    secret: PLACEHOLDER_ENCRYPTED_SECRET
  template:
    metadata:
      name: zerossl-eab-secret
      namespace: cert-manager
    type: Opaque
