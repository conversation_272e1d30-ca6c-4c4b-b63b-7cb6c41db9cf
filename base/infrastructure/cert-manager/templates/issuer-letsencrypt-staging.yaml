apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: issuer-letsencrypt-staging
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  acme:
    email: <EMAIL>
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: issuer-letsencrypt-staging
    solvers:
      - http01:
          gatewayHTTPRoute:
            parentRefs:
            - kind: Gateway
              name: cert-manager-eg
              namespace: default
