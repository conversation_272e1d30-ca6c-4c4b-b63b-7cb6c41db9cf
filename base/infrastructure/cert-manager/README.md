# Cert-Manager with Sealed Secrets

This umbrella chart deploys cert-manager with sealed secrets for secure secret management in GitOps workflows.

## Components

- **Sealed Secrets Controller**: Encrypts secrets that can only be decrypted by the cluster
- **Cert-Manager**: Manages TLS certificates automatically
- **ClusterIssuers**: Pre-configured for Let's Encrypt (staging/production) and ZeroSSL

## Prerequisites

1. Install kubeseal CLI tool:
   ```bash
   # macOS
   brew install kubeseal
   
   # Linux
   KUBESEAL_VERSION='0.29.0'
   curl -OL "https://github.com/bitnami-labs/sealed-secrets/releases/download/v${KUBESEAL_VERSION}/kubeseal-${KUBESEAL_VERSION}-linux-amd64.tar.gz"
   tar -xvzf kubeseal-${KUBESEAL_VERSION}-linux-amd64.tar.gz kubeseal
   sudo install -m 755 kubeseal /usr/local/bin/kubeseal
   ```

2. Ensure envoy-gateway is deployed (required for cert-manager challenge gateway)

## Setup Instructions

### 1. Deploy the Application

The cert-manager application is deployed via ArgoCD. It will automatically:
- Install sealed-secrets controller
- Install cert-manager with Gateway API support
- Create the challenge gateway
- Set up ClusterIssuers (but ZeroSSL will fail until secret is properly encrypted)

### 2. Encrypt the ZeroSSL EAB Secret

After the sealed-secrets controller is running, you need to encrypt the actual ZeroSSL EAB secret:

```bash
# Wait for sealed-secrets controller to be ready
kubectl wait --for=condition=Available deployment/sealed-secrets-controller -n kube-system --timeout=300s

# Encrypt your actual ZeroSSL EAB secret
echo -n "your-actual-zerossl-eab-secret-value" | kubeseal --raw --name zerossl-eab-secret --namespace cert-manager

# This will output an encrypted string like: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEq...
```

### 3. Update the SealedSecret

Replace the placeholder in `templates/zerossl-eab-sealedsecret.yaml`:

```yaml
spec:
  encryptedData:
    secret: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEq...  # Your encrypted secret here
```

Commit and push the changes. ArgoCD will automatically sync and create the proper secret.

## Verification

Check that everything is working:

```bash
# Check sealed-secrets controller
kubectl get pods -n kube-system -l app.kubernetes.io/name=sealed-secrets

# Check cert-manager
kubectl get pods -n cert-manager

# Check ClusterIssuers
kubectl get clusterissuers

# Check that the ZeroSSL secret was created
kubectl get secret zerossl-eab-secret -n cert-manager
```

## Usage

Once deployed, you can request certificates using any of the configured issuers:

```yaml
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: example-tls
  namespace: default
spec:
  secretName: example-tls
  issuerRef:
    name: issuer-letsencrypt-production  # or issuer-letsencrypt-staging, issuer-zerossl-production
    kind: ClusterIssuer
  dnsNames:
    - example.com
    - www.example.com
```

## Security Notes

- The sealed-secrets controller uses asymmetric encryption
- Only the controller running in the cluster can decrypt SealedSecret resources
- The public key is safe to share and is used by kubeseal to encrypt secrets
- Private keys are automatically rotated every 30 days
- Always backup your sealing keys: `kubectl get secret -n kube-system -l sealedsecrets.bitnami.com/sealed-secrets-key -o yaml > backup.yaml`

## Troubleshooting

### SealedSecret not decrypting
- Ensure the sealed-secrets controller is running
- Check that the namespace and name match exactly
- Verify the encrypted data was generated with the correct public key

### Cert-manager challenges failing
- Ensure the envoy-gateway is properly configured
- Check that the cert-manager-eg Gateway is ready
- Verify DNS is pointing to your cluster's ingress

### ZeroSSL issuer not working
- Verify the EAB secret is properly decrypted
- Check the keyID matches your ZeroSSL account
- Ensure the secret key is base64 encoded correctly
