# Cert-Manager Deployment Guide

## Quick Start

1. **Deploy the cert-manager application via ArgoCD:**
   ```bash
   kubectl apply -f clusters/dev-cluster/cert-manager-app.yaml
   ```

2. **Wait for sealed-secrets controller to be ready:**
   ```bash
   kubectl wait --for=condition=Available deployment/sealed-secrets-controller -n kube-system --timeout=300s
   ```

3. **Encrypt your ZeroSSL EAB secret:**
   ```bash
   # Use the helper script
   ./base/infrastructure/cert-manager/scripts/encrypt-zerossl-secret.sh
   
   # Or manually:
   echo -n "your-actual-zerossl-eab-secret" | kubeseal --raw --name zerossl-eab-secret --namespace cert-manager
   ```

4. **Update the SealedSecret file:**
   Replace `PLACEHOLDER_ENCRYPTED_SECRET` in `base/infrastructure/cert-manager/templates/zerossl-eab-sealedsecret.yaml` with your encrypted secret.

5. **Commit and push changes:**
   ```bash
   git add .
   git commit -m "Add encrypted ZeroSSL EAB secret"
   git push
   ```

6. **Verify deployment:**
   ```bash
   # Check all components
   kubectl get pods -n kube-system -l app.kubernetes.io/name=sealed-secrets
   kubectl get pods -n cert-manager
   kubectl get clusterissuers
   kubectl get secret zerossl-eab-secret -n cert-manager
   ```

## What Gets Deployed

- **Sealed Secrets Controller** (in kube-system namespace)
- **Cert-Manager** with Gateway API support (in cert-manager namespace)
- **Challenge Gateway** for ACME HTTP-01 challenges
- **ClusterIssuers** for Let's Encrypt staging, production, and ZeroSSL

## Dependencies

- Envoy Gateway must be deployed first (for the merged-eg GatewayClass)
- ArgoCD must be running in the cluster

## Troubleshooting

If the ZeroSSL issuer shows errors:
1. Verify the secret was properly decrypted: `kubectl get secret zerossl-eab-secret -n cert-manager -o yaml`
2. Check the cert-manager logs: `kubectl logs -n cert-manager deployment/cert-manager`
3. Ensure the keyID in the issuer matches your ZeroSSL account
