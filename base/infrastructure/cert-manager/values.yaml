# Sealed Secrets Controller Configuration
sealed-secrets:
  enabled: true
  fullnameOverride: sealed-secrets-controller
  # Install in kube-system namespace for compatibility with kubeseal CLI defaults
  namespace: kube-system
  
  # Resource limits for sealed-secrets controller
  resources:
    limits:
      cpu: 150m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 128Mi

  # Enable metrics for monitoring
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
      namespace: "prometheus"

# Cert-Manager Configuration  
cert-manager:
  enabled: true
  
  # Install CRDs
  installCRDs: true

  # Enable metrics for monitoring
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
      namespace: "prometheus"

  # Enable Gateway API support for cert-manager
  controller:
    extraArgs:
      - --feature-gates=ExperimentalGatewayAPISupport=true
      - --enable-gateway-api
    
    # Resource limits for cert-manager controller
    resources:
      limits:
        cpu: 200m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi

  # Webhook configuration
  webhook:
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 50m
        memory: 64Mi

  # CA Injector configuration
  cainjector:
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 50m
        memory: 64Mi
