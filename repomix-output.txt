This file is a merged representation of the entire codebase, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded

Additional Info:
----------------

================================================================
Directory Structure
================================================================
argocd-root-apps/
  internal-cluster-core-infra.yaml
base/
  infrastructure/
    envoy-gateway/
      resources/
        custom-proxy-config.yaml
        merged-eg.yaml
      base-values.yaml
      kustomization.yaml
clusters/
  dev-cluster/
    envoy-gateway-app.yaml
    prometheus-operator-app.yaml

================================================================
Files
================================================================

================
File: argocd-root-apps/internal-cluster-core-infra.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dev-cluster-core-infra
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: "https://github.com/wittignl/k8s-clusters"
    path: "clusters/dev-cluster"
    targetRevision: HEAD
  destination:
    server: "https://kubernetes.default.svc"
    namespace: argocd
  syncPolicy:
    automated:
      prune: true
      selfHeal: true

================
File: base/infrastructure/envoy-gateway/resources/custom-proxy-config.yaml
================
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: custom-proxy-config
  namespace: envoy-gateway-system
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  mergeGateways: true

================
File: base/infrastructure/envoy-gateway/resources/merged-eg.yaml
================
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: merged-eg
  namespace: default
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
  parametersRef:
    group: gateway.envoyproxy.io
    kind: EnvoyProxy
    name: custom-proxy-config
    namespace: envoy-gateway-system

================
File: base/infrastructure/envoy-gateway/base-values.yaml
================
envoyGateway:
  gateway:
    controllerName: gateway.envoyproxy.io/gatewayclass-controller
  provider:
    type: Kubernetes
  mergeGateways: true

================
File: base/infrastructure/envoy-gateway/kustomization.yaml
================
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

helmCharts:
  - name: gateway-helm
    repo: oci://docker.io/envoyproxy
    version: "v1.4.1"
    releaseName: eg
    namespace: envoy-gateway-system
    valuesFile: base-values.yaml

resources:
  - resources/custom-proxy-config.yaml
  - resources/merged-eg.yaml

================
File: clusters/dev-cluster/envoy-gateway-app.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: envoy-gateway
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: docker.io/envoyproxy
    chart: gateway-helm
    targetRevision: v1.4.1
    helm:
      releaseName: eg
      valuesFile: base/infastructure/envoy-gateway/base-values.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: envoy-gateway-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true

================
File: clusters/dev-cluster/prometheus-operator-app.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: prometheus-operator
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: registry-1.docker.io/bitnamicharts
    chart: kube-prometheus
    targetRevision: 11.1.17
    helm:
      releaseName: prometheus-operator
  destination:
    server: https://kubernetes.default.svc
    namespace: monitoring
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true



================================================================
End of Codebase
================================================================
